<?php
/**
 * Test script for new URL structure
 * Access via: http://pettingzoo.local/wp-content/themes/generatepress-child/test-url-structure.php
 */

// Load WordPress
require_once('../../../wp-load.php');

echo "<h1>🔧 Petting Zoo URL Structure Test</h1>";

// Test state abbreviations
echo "<h2>State Abbreviations Test</h2>";
$state_abbreviations = get_state_abbreviations();
echo "<p>Florida abbreviation: " . ($state_abbreviations['Florida'] ?? 'Not found') . "</p>";
echo "<p>Texas abbreviation: " . ($state_abbreviations['Texas'] ?? 'Not found') . "</p>";

// Test URL generation functions
echo "<h2>URL Generation Test</h2>";
echo "<p>State URL for Florida: " . get_state_page_url('Florida') . "</p>";
echo "<p>City URL for Miami, Florida: " . get_city_page_url('Miami', 'Florida') . "</p>";

// Test city slug generation
echo "<h2>City Slug Generation Test</h2>";
echo "<p>Miami, Florida slug: " . generate_city_slug_with_state('Miami', 'Florida') . "</p>";
echo "<p>Dallas, Texas slug: " . generate_city_slug_with_state('Dallas', 'Texas') . "</p>";

// Test location taxonomy
echo "<h2>Location Taxonomy Test</h2>";
$location_terms = get_terms(array(
    'taxonomy' => 'location',
    'hide_empty' => false,
    'orderby' => 'parent',
    'order' => 'ASC'
));

if ($location_terms && !is_wp_error($location_terms)) {
    echo "<ul>";
    foreach ($location_terms as $term) {
        if ($term->parent == 0) {
            echo "<li><strong>" . esc_html($term->name) . "</strong> (State - ID: " . $term->term_id . ")";
            
            // Find child cities
            $child_cities = get_terms(array(
                'taxonomy' => 'location',
                'parent' => $term->term_id,
                'hide_empty' => false
            ));
            
            if ($child_cities && !is_wp_error($child_cities)) {
                echo "<ul>";
                foreach ($child_cities as $city) {
                    echo "<li>" . esc_html($city->name) . " (City - ID: " . $city->term_id . ")</li>";
                }
                echo "</ul>";
            }
            echo "</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p>No location terms found or error occurred.</p>";
}

// Test rewrite rules
echo "<h2>Current Rewrite Rules</h2>";
global $wp_rewrite;
$rules = $wp_rewrite->wp_rewrite_rules();
echo "<p>Total rewrite rules: " . count($rules) . "</p>";

// Show first few custom rules
$custom_rules = array();
foreach ($rules as $pattern => $replacement) {
    if (strpos($replacement, 'location=') !== false || strpos($replacement, 'petting_zoo=') !== false) {
        $custom_rules[$pattern] = $replacement;
    }
}

if (!empty($custom_rules)) {
    echo "<h3>Custom Petting Zoo Rules:</h3>";
    echo "<ul>";
    foreach ($custom_rules as $pattern => $replacement) {
        echo "<li><code>" . esc_html($pattern) . "</code> → <code>" . esc_html($replacement) . "</code></li>";
    }
    echo "</ul>";
} else {
    echo "<p>⚠️ No custom petting zoo rewrite rules found!</p>";
}

echo "<h2>Test Links</h2>";
echo "<p>Try these URLs:</p>";
echo "<ul>";
echo "<li><a href='" . home_url('/florida/') . "' target='_blank'>" . home_url('/florida/') . "</a> (State page)</li>";
echo "<li><a href='" . home_url('/florida/miami-fl/') . "' target='_blank'>" . home_url('/florida/miami-fl/') . "</a> (City page)</li>";
echo "<li><a href='" . home_url('/texas/dallas-tx/') . "' target='_blank'>" . home_url('/texas/dallas-tx/') . "</a> (City page)</li>";
echo "</ul>";

echo "<h2>Debug Information</h2>";
echo "<p>WordPress Debug: " . (WP_DEBUG ? 'Enabled' : 'Disabled') . "</p>";
echo "<p>Permalink Structure: " . get_option('permalink_structure') . "</p>";
echo "<p>Home URL: " . home_url() . "</p>";

?>
