<?php
/**
 * Emergency fix script to restore basic functionality
 * Access via: http://pettingzoo.local/emergency-fix.php
 */

// Load WordPress
require_once('wp-load.php');

echo "<h1>🚨 Emergency Fix Script</h1>";

// Flush rewrite rules
flush_rewrite_rules();
echo "<p>✅ Rewrite rules flushed</p>";

// Clear any object cache
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p>✅ Object cache cleared</p>";
}

// Check memory limit
echo "<p>Current memory limit: " . ini_get('memory_limit') . "</p>";
echo "<p>Current memory usage: " . round(memory_get_usage(true) / 1024 / 1024, 2) . " MB</p>";

// Test basic functionality
echo "<h2>Basic Tests</h2>";

// Test if we can create a simple term
$test_term = wp_insert_term('Test State', 'location', array('parent' => 0));
if (!is_wp_error($test_term)) {
    echo "<p>✅ Can create location terms</p>";
    // Clean up
    wp_delete_term($test_term['term_id'], 'location');
} else {
    echo "<p>❌ Error creating terms: " . $test_term->get_error_message() . "</p>";
}

// Test if we can query terms
$terms = get_terms(array('taxonomy' => 'location', 'hide_empty' => false, 'number' => 1));
if (!is_wp_error($terms)) {
    echo "<p>✅ Can query location terms</p>";
} else {
    echo "<p>❌ Error querying terms: " . $terms->get_error_message() . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Try importing a single JSON file now</li>";
echo "<li>If it works, the basic functionality is restored</li>";
echo "<li>If not, check the error logs for more details</li>";
echo "</ol>";

echo "<p><a href='/wp-admin/edit.php?post_type=petting_zoo'>Go to Petting Zoo Admin</a></p>";
?>
