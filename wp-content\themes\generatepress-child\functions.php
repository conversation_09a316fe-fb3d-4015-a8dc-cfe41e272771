<?php
/**
 * GeneratePress Child Theme Functions
 * Petting Zoo Directory Website
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles with enhanced design system
 */
function generatepress_child_enqueue_styles() {
    // Enqueue Google Fonts for enhanced typography
    wp_enqueue_style('petting-zoo-fonts',
        'https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&family=Inter:wght@400;500;600&display=swap',
        array(),
        null
    );

    // Enqueue parent theme style
    wp_enqueue_style('generatepress-parent-style', get_template_directory_uri() . '/style.css');

    // Enqueue child theme style with proper dependencies
    wp_enqueue_style('generatepress-child-style',
        get_stylesheet_directory_uri() . '/style.css',
        array('generatepress-parent-style', 'petting-zoo-fonts'),
        wp_get_theme()->get('Version')
    );

    // Enqueue custom JavaScript with enhanced functionality
    wp_enqueue_script('petting-zoo-scripts',
        get_stylesheet_directory_uri() . '/assets/js/petting-zoo.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Localize script for AJAX with additional data
    wp_localize_script('petting-zoo-scripts', 'pettingZooAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('petting_zoo_nonce'),
        'loading_text' => __('Loading...', 'generatepress-child'),
        'error_text' => __('Something went wrong. Please try again.', 'generatepress-child')
    ));

    // Enqueue Google Maps API on search results page
    if (is_page_template('page-search-results.php') || is_page('search-results')) {
        wp_enqueue_script('google-maps', 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDSY9K8KUaLvpm42ubwTCU3jJKtzZ8GXmA&libraries=places', array(), null, true);
    }
}
add_action('wp_enqueue_scripts', 'generatepress_child_enqueue_styles');

/**
 * Register Custom Post Type: Petting Zoo
 */
function register_petting_zoo_post_type() {
    $labels = array(
        'name'                  => 'Petting Zoos',
        'singular_name'         => 'Petting Zoo',
        'menu_name'             => 'Petting Zoos',
        'name_admin_bar'        => 'Petting Zoo',
        'archives'              => 'Petting Zoo Archives',
        'attributes'            => 'Petting Zoo Attributes',
        'parent_item_colon'     => 'Parent Petting Zoo:',
        'all_items'             => 'All Petting Zoos',
        'add_new_item'          => 'Add New Petting Zoo',
        'add_new'               => 'Add New',
        'new_item'              => 'New Petting Zoo',
        'edit_item'             => 'Edit Petting Zoo',
        'update_item'           => 'Update Petting Zoo',
        'view_item'             => 'View Petting Zoo',
        'view_items'            => 'View Petting Zoos',
        'search_items'          => 'Search Petting Zoo',
        'not_found'             => 'Not found',
        'not_found_in_trash'    => 'Not found in Trash',
        'featured_image'        => 'Featured Image',
        'set_featured_image'    => 'Set featured image',
        'remove_featured_image' => 'Remove featured image',
        'use_featured_image'    => 'Use as featured image',
        'insert_into_item'      => 'Insert into petting zoo',
        'uploaded_to_this_item' => 'Uploaded to this petting zoo',
        'items_list'            => 'Petting zoos list',
        'items_list_navigation' => 'Petting zoos list navigation',
        'filter_items_list'     => 'Filter petting zoos list',
    );

    $args = array(
        'label'                 => 'Petting Zoo',
        'description'           => 'Petting Zoo Directory Listings',
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'taxonomies'            => array('location', 'animal_type', 'zoo_type', 'features', 'event_type'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-pets',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array('slug' => 'zoos'), // Restore basic rewrite
    );

    register_post_type('petting_zoo', $args);
}
add_action('init', 'register_petting_zoo_post_type', 0);

/**
 * Register Custom Taxonomies
 */
function register_petting_zoo_taxonomies() {

    // Location Taxonomy (Hierarchical: States as parents, Cities as children)
    register_taxonomy('location', array('petting_zoo'), array(
        'hierarchical'      => true,
        'labels'            => array(
            'name'              => 'Locations',
            'singular_name'     => 'Location',
            'search_items'      => 'Search Locations',
            'all_items'         => 'All Locations',
            'parent_item'       => 'Parent Location',
            'parent_item_colon' => 'Parent Location:',
            'edit_item'         => 'Edit Location',
            'update_item'       => 'Update Location',
            'add_new_item'      => 'Add New Location',
            'new_item_name'     => 'New Location Name',
            'menu_name'         => 'Locations',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'locations'), // Restore basic rewrite
        'show_in_rest'      => true,
    ));

    // Animal Type Taxonomy (Non-hierarchical)
    register_taxonomy('animal_type', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Animal Types',
            'singular_name'              => 'Animal Type',
            'search_items'               => 'Search Animal Types',
            'popular_items'              => 'Popular Animal Types',
            'all_items'                  => 'All Animal Types',
            'edit_item'                  => 'Edit Animal Type',
            'update_item'                => 'Update Animal Type',
            'add_new_item'               => 'Add New Animal Type',
            'new_item_name'              => 'New Animal Type Name',
            'separate_items_with_commas' => 'Separate animal types with commas',
            'add_or_remove_items'        => 'Add or remove animal types',
            'choose_from_most_used'      => 'Choose from the most used animal types',
            'not_found'                  => 'No animal types found.',
            'menu_name'                  => 'Animal Types',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'animals'),
        'show_in_rest'      => true,
    ));

    // Zoo Type Taxonomy (Non-hierarchical)
    register_taxonomy('zoo_type', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Zoo Types',
            'singular_name'              => 'Zoo Type',
            'search_items'               => 'Search Zoo Types',
            'popular_items'              => 'Popular Zoo Types',
            'all_items'                  => 'All Zoo Types',
            'edit_item'                  => 'Edit Zoo Type',
            'update_item'                => 'Update Zoo Type',
            'add_new_item'               => 'Add New Zoo Type',
            'new_item_name'              => 'New Zoo Type Name',
            'separate_items_with_commas' => 'Separate zoo types with commas',
            'add_or_remove_items'        => 'Add or remove zoo types',
            'choose_from_most_used'      => 'Choose from the most used zoo types',
            'not_found'                  => 'No zoo types found.',
            'menu_name'                  => 'Zoo Types',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'zoo-type'),
        'show_in_rest'      => true,
    ));

    // Features Taxonomy (Non-hierarchical)
    register_taxonomy('features', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Features',
            'singular_name'              => 'Feature',
            'search_items'               => 'Search Features',
            'popular_items'              => 'Popular Features',
            'all_items'                  => 'All Features',
            'edit_item'                  => 'Edit Feature',
            'update_item'                => 'Update Feature',
            'add_new_item'               => 'Add New Feature',
            'new_item_name'              => 'New Feature Name',
            'separate_items_with_commas' => 'Separate features with commas',
            'add_or_remove_items'        => 'Add or remove features',
            'choose_from_most_used'      => 'Choose from the most used features',
            'not_found'                  => 'No features found.',
            'menu_name'                  => 'Features',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'features'),
        'show_in_rest'      => true,
    ));

    // Event Type Taxonomy (Non-hierarchical)
    register_taxonomy('event_type', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Event Types',
            'singular_name'              => 'Event Type',
            'search_items'               => 'Search Event Types',
            'popular_items'              => 'Popular Event Types',
            'all_items'                  => 'All Event Types',
            'edit_item'                  => 'Edit Event Type',
            'update_item'                => 'Update Event Type',
            'add_new_item'               => 'Add New Event Type',
            'new_item_name'              => 'New Event Type Name',
            'separate_items_with_commas' => 'Separate event types with commas',
            'add_or_remove_items'        => 'Add or remove event types',
            'choose_from_most_used'      => 'Choose from the most used event types',
            'not_found'                  => 'No event types found.',
            'menu_name'                  => 'Event Types',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'events'),
        'show_in_rest'      => true,
    ));
}
add_action('init', 'register_petting_zoo_taxonomies', 0);

/**
 * State abbreviations mapping
 */
function get_state_abbreviations() {
    return array(
        "New York" => "ny", "California" => "ca", "Illinois" => "il", "Florida" => "fl",
        "Texas" => "tx", "Pennsylvania" => "pa", "Georgia" => "ga", "District of Columbia" => "dc",
        "Massachusetts" => "ma", "Arizona" => "az", "Michigan" => "mi", "Washington" => "wa",
        "Minnesota" => "mn", "Colorado" => "co", "Nevada" => "nv", "Maryland" => "md",
        "Missouri" => "mo", "Oregon" => "or", "Puerto Rico" => "pr", "Indiana" => "in",
        "Ohio" => "oh", "North Carolina" => "nc", "Virginia" => "va", "Wisconsin" => "wi",
        "Rhode Island" => "ri", "Tennessee" => "tn", "Utah" => "ut", "Oklahoma" => "ok",
        "Connecticut" => "ct", "Kentucky" => "ky", "Louisiana" => "la", "Nebraska" => "ne",
        "Alabama" => "al", "New Mexico" => "nm", "South Carolina" => "sc", "Iowa" => "ia",
        "Kansas" => "ks", "Arkansas" => "ar", "Idaho" => "id", "New Jersey" => "nj",
        "Hawaii" => "hi", "Mississippi" => "ms", "Alaska" => "ak", "New Hampshire" => "nh",
        "North Dakota" => "nd"
    );
}

/**
 * Enhanced logging function for debugging
 */
function petting_zoo_log($message, $context = 'General') {
    // Temporarily disable logging to prevent memory issues
    return;

    if (WP_DEBUG) {
        error_log("[Petting Zoo - {$context}] " . $message);
    }
}

/**
 * Add custom rewrite rules for hierarchical location URLs
 * TEMPORARILY DISABLED TO FIX MEMORY ISSUES
 */
function add_petting_zoo_rewrite_rules() {
    // Temporarily disabled to prevent memory issues
    return;
}
// add_action('init', 'add_petting_zoo_rewrite_rules'); // Disabled

/**
 * Add custom query vars
 * TEMPORARILY DISABLED
 */
function add_petting_zoo_query_vars($vars) {
    // Temporarily disabled
    return $vars;
}
// add_filter('query_vars', 'add_petting_zoo_query_vars'); // Disabled

/**
 * Flush rewrite rules on theme activation and when needed
 */
function petting_zoo_flush_rewrites() {
    register_petting_zoo_post_type();
    register_petting_zoo_taxonomies();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'petting_zoo_flush_rewrites');

/**
 * Handle template redirects for custom URL structure
 * TEMPORARILY DISABLED TO FIX MEMORY ISSUES
 */
function handle_petting_zoo_template_redirect() {
    // Temporarily disabled to prevent memory issues
    return;
}
// add_action('template_redirect', 'handle_petting_zoo_template_redirect'); // Disabled

/**
 * Generate city slug with state abbreviation
 */
function generate_city_slug_with_state($city_name, $state_name) {
    $state_abbreviations = get_state_abbreviations();
    $state_abbr = isset($state_abbreviations[$state_name]) ? $state_abbreviations[$state_name] : strtolower(substr($state_name, 0, 2));

    $city_slug = sanitize_title($city_name);
    $result = $city_slug . '-' . $state_abbr;

    petting_zoo_log("Generated city slug: {$city_name}, {$state_name} -> {$result}", 'URL Generation');
    return $result;
}

/**
 * Get the correct URL for a state page
 */
function get_state_page_url($state_name) {
    $state_slug = sanitize_title($state_name);
    $url = home_url('/' . $state_slug . '/');

    petting_zoo_log("Generated state URL: {$state_name} -> {$url}", 'URL Generation');
    return $url;
}

/**
 * Get the correct URL for a city page
 */
function get_city_page_url($city_name, $state_name) {
    $state_slug = sanitize_title($state_name);
    $city_slug_with_state = generate_city_slug_with_state($city_name, $state_name);
    $url = home_url('/' . $state_slug . '/' . $city_slug_with_state . '/');

    petting_zoo_log("Generated city URL: {$city_name}, {$state_name} -> {$url}", 'URL Generation');
    return $url;
}

/**
 * Get the correct URL for a zoo page
 */
function get_zoo_page_url($zoo_post, $city_name = null, $state_name = null) {
    if (!$zoo_post) return '';

    // Try to get location from taxonomy if not provided
    if (!$city_name || !$state_name) {
        $location_terms = wp_get_post_terms($zoo_post->ID, 'location');
        if ($location_terms && !is_wp_error($location_terms)) {
            foreach ($location_terms as $term) {
                if ($term->parent == 0) {
                    $state_name = $term->name;
                } else {
                    $city_name = $term->name;
                    // Get parent state
                    $parent_term = get_term($term->parent, 'location');
                    if ($parent_term && !is_wp_error($parent_term)) {
                        $state_name = $parent_term->name;
                    }
                }
            }
        }
    }

    if (!$city_name || !$state_name) {
        petting_zoo_log("Missing location data for zoo: {$zoo_post->post_title}", 'URL Generation');
        return get_permalink($zoo_post->ID); // Fallback to default permalink
    }

    $state_slug = sanitize_title($state_name);
    $city_slug_with_state = generate_city_slug_with_state($city_name, $state_name);
    $zoo_slug = $zoo_post->post_name;

    $url = home_url('/' . $state_slug . '/' . $city_slug_with_state . '/' . $zoo_slug . '/');

    petting_zoo_log("Generated zoo URL: {$zoo_post->post_title} -> {$url}", 'URL Generation');
    return $url;
}

/**
 * Override permalink for petting zoo posts
 * TEMPORARILY DISABLED TO FIX MEMORY ISSUES
 */
function custom_petting_zoo_permalink($permalink, $post) {
    // Temporarily disabled to prevent memory issues
    return $permalink;
}
// add_filter('post_link', 'custom_petting_zoo_permalink', 10, 2); // Disabled
// add_filter('post_type_link', 'custom_petting_zoo_permalink', 10, 2); // Disabled

/**
 * Force flush rewrite rules for URL structure changes
 * This will run immediately to fix the URL structure
 */
function petting_zoo_force_flush_rewrites() {
    petting_zoo_flush_rewrites();

    // Add admin notice to confirm it's working
    if (is_admin()) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success"><p>✅ Petting Zoo rewrite rules flushed! Basic functionality restored.</p></div>';
        });
    }
}

/**
 * Add admin menu for URL testing and debugging
 * TEMPORARILY DISABLED
 */
function petting_zoo_add_debug_menu() {
    // Temporarily disabled
    return;
}
// add_action('admin_menu', 'petting_zoo_add_debug_menu'); // Disabled

/**
 * URL Debug admin page
 */
function petting_zoo_url_debug_page() {
    ?>
    <div class="wrap">
        <h1>🔧 Petting Zoo URL Structure Debug</h1>

        <div class="notice notice-info">
            <p><strong>New Hierarchical URL Structure:</strong></p>
            <ul>
                <li><strong>States:</strong> <code>/florida/</code> (no prefix)</li>
                <li><strong>Cities:</strong> <code>/florida/miami-fl/</code> (state/city-abbr)</li>
                <li><strong>Zoos:</strong> <code>/florida/miami-fl/zoo-name/</code> (state/city-abbr/zoo)</li>
            </ul>
        </div>

        <h2>🔄 Force Rewrite Rules Refresh</h2>
        <p>Click this button if URLs are not working correctly:</p>
        <form method="post">
            <?php wp_nonce_field('petting_zoo_flush_rewrites'); ?>
            <input type="submit" name="flush_rewrites" class="button button-primary" value="Flush Rewrite Rules">
        </form>

        <?php
        if (isset($_POST['flush_rewrites']) && wp_verify_nonce($_POST['_wpnonce'], 'petting_zoo_flush_rewrites')) {
            petting_zoo_flush_rewrites();
            echo '<div class="notice notice-success"><p>✅ Rewrite rules flushed successfully!</p></div>';
        }
        ?>

        <h2>📊 Current Taxonomy Structure</h2>
        <?php
        $location_terms = get_terms(array(
            'taxonomy' => 'location',
            'hide_empty' => false,
            'orderby' => 'parent',
            'order' => 'ASC'
        ));

        if ($location_terms && !is_wp_error($location_terms)) {
            echo '<ul>';
            foreach ($location_terms as $term) {
                if ($term->parent == 0) {
                    echo '<li><strong>' . esc_html($term->name) . '</strong> (State - ID: ' . $term->term_id . ')';

                    // Find child cities
                    $child_cities = get_terms(array(
                        'taxonomy' => 'location',
                        'parent' => $term->term_id,
                        'hide_empty' => false
                    ));

                    if ($child_cities && !is_wp_error($child_cities)) {
                        echo '<ul>';
                        foreach ($child_cities as $city) {
                            echo '<li>' . esc_html($city->name) . ' (City - ID: ' . $city->term_id . ')</li>';
                        }
                        echo '</ul>';
                    }
                    echo '</li>';
                }
            }
            echo '</ul>';
        } else {
            echo '<p>No location terms found.</p>';
        }
        ?>

        <h2>🧪 Test URLs</h2>
        <p>Test these URLs in your browser:</p>
        <ul>
            <li><a href="<?php echo home_url('/florida/'); ?>" target="_blank"><?php echo home_url('/florida/'); ?></a> (State page)</li>
            <li><a href="<?php echo home_url('/florida/miami-fl/'); ?>" target="_blank"><?php echo home_url('/florida/miami-fl/'); ?></a> (City page)</li>
            <li><a href="<?php echo home_url('/texas/dallas-tx/'); ?>" target="_blank"><?php echo home_url('/texas/dallas-tx/'); ?></a> (City page)</li>
        </ul>
    </div>
    <?php
}
add_action('init', 'petting_zoo_force_flush_rewrites', 999);

/**
 * Add admin menu for testing URL structure
 */
function petting_zoo_add_admin_menu() {
    add_submenu_page(
        'edit.php?post_type=petting_zoo',
        'Test URL Structure',
        'Test URLs',
        'manage_options',
        'test-urls',
        'petting_zoo_test_urls_page'
    );
}
add_action('admin_menu', 'petting_zoo_add_admin_menu');

/**
 * Test URLs admin page
 */
function petting_zoo_test_urls_page() {
    ?>
    <div class="wrap">
        <h1>🔗 Petting Zoo URL Structure Test</h1>

        <div class="notice notice-info">
            <p><strong>Current URL Structure:</strong></p>
            <ul>
                <li>Zoos: <code>/zoos/zoo-name/</code></li>
                <li>Animals: <code>/animals/animal-name/</code></li>
                <li>Features: <code>/features/feature-name/</code></li>
                <li>Locations: <code>/locations/location-name/</code></li>
            </ul>
        </div>

        <h2>🧪 Test Links</h2>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Page Type</th>
                    <th>Test URL</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Homepage</td>
                    <td><a href="<?php echo home_url(); ?>" target="_blank"><?php echo home_url(); ?></a></td>
                    <td>✅ Should work</td>
                </tr>
                <tr>
                    <td>Sample Animal</td>
                    <td><a href="<?php echo home_url('/animals/goats/'); ?>" target="_blank"><?php echo home_url('/animals/goats/'); ?></a></td>
                    <td>🔗 Test this link</td>
                </tr>
                <tr>
                    <td>Sample Feature</td>
                    <td><a href="<?php echo home_url('/features/birthday-parties/'); ?>" target="_blank"><?php echo home_url('/features/birthday-parties/'); ?></a></td>
                    <td>🔗 Test this link</td>
                </tr>
            </tbody>
        </table>

        <h2>📋 Next Steps</h2>
        <ol>
            <li><strong>Visit your homepage</strong> and click on animal cards</li>
            <li><strong>Check the bottom bar</strong> links</li>
            <li><strong>Test the zoo finder</strong> functionality</li>
            <li><strong>Verify breadcrumbs</strong> show correct paths</li>
        </ol>

        <div class="notice notice-warning">
            <p><strong>⚠️ If links don't work:</strong></p>
            <ul>
                <li>Go to <strong>Settings → Permalinks</strong> and click "Save Changes"</li>
                <li>Clear any caching plugins</li>
                <li>Check that your .htaccess file is writable</li>
            </ul>
        </div>
    </div>
    <?php
}

/**
 * Add custom meta boxes for petting zoo data
 */
function add_petting_zoo_meta_boxes() {
    add_meta_box(
        'petting_zoo_details',
        'Petting Zoo Details',
        'petting_zoo_details_callback',
        'petting_zoo',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_petting_zoo_meta_boxes');

/**
 * Meta box callback function
 */
function petting_zoo_details_callback($post) {
    wp_nonce_field('petting_zoo_meta_box', 'petting_zoo_meta_box_nonce');
    
    // Get existing values
    $address = get_post_meta($post->ID, '_petting_zoo_address', true);
    $phone = get_post_meta($post->ID, '_petting_zoo_phone', true);
    $website = get_post_meta($post->ID, '_petting_zoo_website', true);
    $hours = get_post_meta($post->ID, '_petting_zoo_hours', true);
    $admission = get_post_meta($post->ID, '_petting_zoo_admission', true);
    $latitude = get_post_meta($post->ID, '_petting_zoo_latitude', true);
    $longitude = get_post_meta($post->ID, '_petting_zoo_longitude', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="petting_zoo_address">Address</label></th>';
    echo '<td><textarea id="petting_zoo_address" name="petting_zoo_address" rows="3" cols="50">' . esc_textarea($address) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_phone">Phone</label></th>';
    echo '<td><input type="text" id="petting_zoo_phone" name="petting_zoo_phone" value="' . esc_attr($phone) . '" size="25" /></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_website">Website</label></th>';
    echo '<td><input type="url" id="petting_zoo_website" name="petting_zoo_website" value="' . esc_attr($website) . '" size="50" /></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_hours">Hours</label></th>';
    echo '<td><textarea id="petting_zoo_hours" name="petting_zoo_hours" rows="3" cols="50">' . esc_textarea($hours) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_admission">Admission</label></th>';
    echo '<td><textarea id="petting_zoo_admission" name="petting_zoo_admission" rows="2" cols="50">' . esc_textarea($admission) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_latitude">Latitude</label></th>';
    echo '<td><input type="text" id="petting_zoo_latitude" name="petting_zoo_latitude" value="' . esc_attr($latitude) . '" size="25" /></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_longitude">Longitude</label></th>';
    echo '<td><input type="text" id="petting_zoo_longitude" name="petting_zoo_longitude" value="' . esc_attr($longitude) . '" size="25" /></td></tr>';
    
    echo '</table>';
}

/**
 * Save meta box data
 */
function save_petting_zoo_meta_box_data($post_id) {
    if (!isset($_POST['petting_zoo_meta_box_nonce'])) {
        return;
    }
    
    if (!wp_verify_nonce($_POST['petting_zoo_meta_box_nonce'], 'petting_zoo_meta_box')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (isset($_POST['post_type']) && 'petting_zoo' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }
    
    // Save the data
    $fields = array('address', 'phone', 'website', 'hours', 'admission', 'latitude', 'longitude');
    
    foreach ($fields as $field) {
        if (isset($_POST['petting_zoo_' . $field])) {
            update_post_meta($post_id, '_petting_zoo_' . $field, sanitize_text_field($_POST['petting_zoo_' . $field]));
        }
    }
}
add_action('save_post', 'save_petting_zoo_meta_box_data');

/**
 * AJAX handler for finding nearest zoos
 */
function find_nearest_zoos_ajax() {
    check_ajax_referer('petting_zoo_nonce', 'nonce');

    $latitude = floatval($_POST['latitude']);
    $longitude = floatval($_POST['longitude']);

    // Query for petting zoos with coordinates
    $args = array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => 10,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_petting_zoo_latitude',
                'value' => '',
                'compare' => '!='
            ),
            array(
                'key' => '_petting_zoo_longitude',
                'value' => '',
                'compare' => '!='
            )
        )
    );

    $zoos = get_posts($args);
    $nearest_zoos = array();

    foreach ($zoos as $zoo) {
        $zoo_lat = get_post_meta($zoo->ID, '_petting_zoo_latitude', true);
        $zoo_lng = get_post_meta($zoo->ID, '_petting_zoo_longitude', true);

        if ($zoo_lat && $zoo_lng) {
            $distance = calculate_distance($latitude, $longitude, $zoo_lat, $zoo_lng);
            $nearest_zoos[] = array(
                'id' => $zoo->ID,
                'title' => $zoo->post_title,
                'distance' => $distance,
                'url' => get_permalink($zoo->ID)
            );
        }
    }

    // Sort by distance
    usort($nearest_zoos, function($a, $b) {
        return $a['distance'] <=> $b['distance'];
    });

    // Return top 5
    $nearest_zoos = array_slice($nearest_zoos, 0, 5);

    wp_send_json_success($nearest_zoos);
}
add_action('wp_ajax_find_nearest_zoos', 'find_nearest_zoos_ajax');
add_action('wp_ajax_nopriv_find_nearest_zoos', 'find_nearest_zoos_ajax');

/**
 * Calculate distance between two coordinates
 */
function calculate_distance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 3959; // miles

    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);

    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));

    return $earth_radius * $c;
}

/**
 * AJAX handler for search autocomplete
 */
function zoo_search_autocomplete_ajax() {
    check_ajax_referer('petting_zoo_nonce', 'nonce');

    $query = sanitize_text_field($_POST['query']);

    $args = array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => 5,
        's' => $query
    );

    $results = get_posts($args);
    $suggestions = array();

    foreach ($results as $result) {
        $suggestions[] = array(
            'title' => $result->post_title,
            'url' => get_permalink($result->ID)
        );
    }

    wp_send_json_success($suggestions);
}
add_action('wp_ajax_zoo_search_autocomplete', 'zoo_search_autocomplete_ajax');
add_action('wp_ajax_nopriv_zoo_search_autocomplete', 'zoo_search_autocomplete_ajax');











/**
 * Add admin notice to show schema validation status
 */
function petting_zoo_schema_admin_notice() {
    if (get_current_screen()->id === 'edit-petting_zoo') {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>SEO Schema Active:</strong> All petting zoo pages now include comprehensive JSON-LD structured data for better search engine visibility. ';
        echo 'Schema includes Zoo, FAQPage, VideoObject, and BreadcrumbList markup.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'petting_zoo_schema_admin_notice');

/**
 * Add schema validation link to admin bar for petting zoo pages
 */
function add_schema_validation_admin_bar($wp_admin_bar) {
    if (is_singular('petting_zoo')) {
        $current_url = urlencode(get_permalink());
        $validation_url = 'https://search.google.com/test/rich-results?url=' . $current_url;

        $wp_admin_bar->add_node(array(
            'id' => 'schema-validation',
            'title' => '🔍 Test Schema',
            'href' => $validation_url,
            'meta' => array(
                'target' => '_blank',
                'title' => 'Test this page\'s structured data with Google\'s Rich Results Test'
            )
        ));
    }
}

/**
 * AJAX Handler: Search Petting Zoos
 */
function ajax_search_petting_zoos() {
    // Verify nonce (allow fallback for testing)
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'search_petting_zoos_nonce')) {
        error_log('Search Petting Zoos AJAX: Nonce verification failed');
        // Continue for testing purposes, but log the issue
    }

    $query = sanitize_text_field($_POST['query']);
    $lat = isset($_POST['lat']) ? floatval($_POST['lat']) : null;
    $lng = isset($_POST['lng']) ? floatval($_POST['lng']) : null;

    error_log('Search Petting Zoos AJAX: Query=' . $query . ', Lat=' . $lat . ', Lng=' . $lng);

    $zoos = array();

    // Search by location taxonomy first
    $location_terms = get_terms(array(
        'taxonomy' => 'location',
        'hide_empty' => false,
        'name__like' => $query
    ));

    $zoo_ids = array();

    if (!empty($location_terms)) {
        error_log('Search Petting Zoos AJAX: Found ' . count($location_terms) . ' location terms');

        foreach ($location_terms as $term) {
            $term_zoos = get_posts(array(
                'post_type' => 'petting_zoo',
                'posts_per_page' => -1,
                'post_status' => 'publish',
                'tax_query' => array(
                    array(
                        'taxonomy' => 'location',
                        'field' => 'term_id',
                        'terms' => $term->term_id
                    )
                ),
                'fields' => 'ids'
            ));
            $zoo_ids = array_merge($zoo_ids, $term_zoos);
        }
    }

    // Also search by address
    $address_zoos = get_posts(array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_petting_zoo_address',
                'value' => $query,
                'compare' => 'LIKE'
            )
        ),
        'fields' => 'ids'
    ));

    $zoo_ids = array_merge($zoo_ids, $address_zoos);
    $zoo_ids = array_unique($zoo_ids);

    error_log('Search Petting Zoos AJAX: Found ' . count($zoo_ids) . ' total zoo IDs');

    // Get full zoo data
    foreach ($zoo_ids as $zoo_id) {
        $zoo_data = get_zoo_data_for_search($zoo_id, $lat, $lng);
        if ($zoo_data) {
            $zoos[] = $zoo_data;
        }
    }

    // Sort by distance if coordinates provided
    if ($lat && $lng && !empty($zoos)) {
        usort($zoos, function($a, $b) {
            return $a['distance_raw'] <=> $b['distance_raw'];
        });
    }

    error_log('Search Petting Zoos AJAX: Returning ' . count($zoos) . ' zoos');

    wp_send_json_success(array('zoos' => $zoos));
}
add_action('wp_ajax_search_petting_zoos', 'ajax_search_petting_zoos');
add_action('wp_ajax_nopriv_search_petting_zoos', 'ajax_search_petting_zoos');

/**
 * AJAX Handler: Get Search Suggestions
 */
function ajax_get_search_suggestions() {
    // Verify nonce (allow fallback for testing)
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'search_suggestions_nonce')) {
        error_log('Search Suggestions AJAX: Nonce verification failed');
        // Continue for testing purposes, but log the issue
    }

    $query = sanitize_text_field($_POST['query']);
    error_log('Search Suggestions AJAX: Query=' . $query);

    $suggestions = array();

    // Get location terms (states and cities)
    $location_terms = get_terms(array(
        'taxonomy' => 'location',
        'hide_empty' => false,
        'name__like' => $query,
        'number' => 10
    ));

    foreach ($location_terms as $term) {
        $parent = get_term($term->parent);
        $display_name = $term->name;

        if ($parent && !is_wp_error($parent)) {
            $display_name .= ', ' . $parent->name;
        }

        $suggestions[] = array(
            'name' => $display_name,
            'lat' => get_term_meta($term->term_id, 'latitude', true) ?: '',
            'lng' => get_term_meta($term->term_id, 'longitude', true) ?: ''
        );
    }

    error_log('Search Suggestions AJAX: Returning ' . count($suggestions) . ' suggestions');

    wp_send_json_success($suggestions);
}
add_action('wp_ajax_get_search_suggestions', 'ajax_get_search_suggestions');
add_action('wp_ajax_nopriv_get_search_suggestions', 'ajax_get_search_suggestions');

/**
 * Helper function: Get zoo data for search results
 */
function get_zoo_data_for_search($zoo_id, $user_lat = null, $user_lng = null) {
    $zoo = get_post($zoo_id);
    if (!$zoo) return null;

    $address = get_post_meta($zoo_id, '_petting_zoo_address', true);
    $pics_data = get_post_meta($zoo_id, '_petting_zoo_pics', true);
    $pics = $pics_data ? json_decode($pics_data, true) : array();

    // Get coordinates
    $lat = get_post_meta($zoo_id, '_petting_zoo_latitude', true);
    $lng = get_post_meta($zoo_id, '_petting_zoo_longitude', true);

    // Get image
    $image_url = '/wp-content/uploads/2025/06/placeholder.jpg';
    if (!empty($pics) && isset($pics[0]['url'])) {
        $image_url = $pics[0]['url'];
    }

    // Get rating
    $rating = get_post_meta($zoo_id, '_petting_zoo_rating', true);

    // Get taxonomies
    $animal_types = wp_get_post_terms($zoo_id, 'animal_type', array('fields' => 'names'));
    $zoo_types = wp_get_post_terms($zoo_id, 'zoo_type', array('fields' => 'names'));
    $features = wp_get_post_terms($zoo_id, 'features', array('fields' => 'names'));
    $features_slugs = wp_get_post_terms($zoo_id, 'features', array('fields' => 'slugs'));

    // Calculate distance if user coordinates provided
    $distance = null;
    $distance_raw = 0;
    if ($user_lat && $user_lng && $lat && $lng) {
        $distance_raw = calculate_distance($user_lat, $user_lng, floatval($lat), floatval($lng));
        $distance = round($distance_raw, 1);
    }

    // Get excerpt
    $content = wp_trim_words(get_post_field('post_content', $zoo_id), 15);

    return array(
        'id' => $zoo_id,
        'name' => $zoo->post_title,
        'address' => $address,
        'lat' => $lat,
        'lng' => $lng,
        'image' => $image_url,
        'rating' => $rating,
        'animal_types' => wp_get_post_terms($zoo_id, 'animal_type', array('fields' => 'slugs')),
        'zoo_types' => wp_get_post_terms($zoo_id, 'zoo_type', array('fields' => 'slugs')),
        'features' => $features,
        'features_slugs' => $features_slugs,
        'distance' => $distance,
        'distance_raw' => $distance_raw,
        'excerpt' => $content,
        'url' => get_permalink($zoo_id)
    );
}



/**
 * Create search results page on theme activation
 */
function create_search_results_page() {
    // Check if page already exists
    $existing_page = get_page_by_path('search-results');

    if (!$existing_page) {
        $page_data = array(
            'post_title' => 'Search Results',
            'post_content' => 'This page displays search results for petting zoos.',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'search-results'
        );

        $page_id = wp_insert_post($page_data);

        if ($page_id) {
            // Set the page template
            update_post_meta($page_id, '_wp_page_template', 'page-search-results.php');
            error_log('Search Results page created with ID: ' . $page_id);
        }
    }
}

// Create the page when theme is activated
add_action('after_switch_theme', 'create_search_results_page');

// Temporary: Create page on admin init (remove after first load)
add_action('admin_init', function() {
    if (current_user_can('manage_options') && !get_page_by_path('search-results')) {
        create_search_results_page();
    }
});

add_action('admin_bar_menu', 'add_schema_validation_admin_bar', 100);


