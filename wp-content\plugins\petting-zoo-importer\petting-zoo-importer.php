<?php
/**
 * Plugin Name: Petting Zoo Importer
 * Description: Custom importer for petting zoo data from JSON files
 * Version: 1.0.0
 * Author: Custom Development
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PettingZooImporter {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_simulate_import', array($this, 'simulate_import'));
        add_action('wp_ajax_process_import', array($this, 'process_import'));
        add_action('wp_ajax_bulk_import', array($this, 'bulk_import'));
        add_action('wp_ajax_clear_all_data', array($this, 'clear_all_data'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=petting_zoo',
            'Import Petting Zoos',
            'Import Data',
            'manage_options',
            'petting-zoo-importer',
            array($this, 'admin_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'petting_zoo_page_petting-zoo-importer') {
            return;
        }
        
        wp_enqueue_script('petting-zoo-importer-js', 
            plugin_dir_url(__FILE__) . 'assets/importer.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('petting-zoo-importer-js', 'importerAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('petting_zoo_importer_nonce')
        ));
        
        wp_enqueue_style('petting-zoo-importer-css',
            plugin_dir_url(__FILE__) . 'assets/importer.css',
            array(),
            '1.0.0'
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>Petting Zoo Data Importer</h1>
            <p>Import petting zoo data from JSON files. Use "Simulate" to test the import without making changes, then "Import" to actually import the data.</p>

            <div class="importer-container">
                <div class="importer-left-column">
                    <!-- Single File Import Section -->
                    <div class="file-selection">
                        <h2>Select JSON File</h2>
                        <input type="file" id="json-file" accept=".json" />
                        <p class="description">Select a JSON file containing petting zoo data.</p>

                        <div class="import-actions">
                            <button id="simulate-btn" class="button button-secondary" disabled>
                                <span class="dashicons dashicons-search"></span>
                                Simulate Import
                            </button>
                            <button id="import-btn" class="button button-primary" disabled>
                                <span class="dashicons dashicons-download"></span>
                                Import Data
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Import Section -->
                    <div class="bulk-import-section">
                        <h2>Bulk Import JSON Files</h2>
                        <input type="file" id="bulk-json-files" accept=".json" multiple />
                        <p class="description">Select multiple JSON files for bulk importing. All files will be processed sequentially.</p>

                        <div class="bulk-import-actions">
                            <button id="bulk-simulate-btn" class="button button-secondary" disabled>
                                <span class="dashicons dashicons-search"></span>
                                Simulate Bulk Import
                            </button>
                            <button id="bulk-import-btn" class="button button-primary" disabled>
                                <span class="dashicons dashicons-download"></span>
                                Bulk Import Data
                            </button>
                        </div>
                    </div>

                    <!-- Developer Tools Section -->
                    <div class="developer-tools-section">
                        <h2>Developer Tools</h2>
                        <p class="description">⚠️ <strong>Warning:</strong> These actions are irreversible and will permanently delete data.</p>

                        <div class="developer-actions">
                            <button id="clear-all-btn" class="button button-secondary button-danger">
                                <span class="dashicons dashicons-trash"></span>
                                Clear All Petting Zoo Data
                            </button>
                        </div>
                        <p class="description">This will delete all petting zoos, locations, animals, and related data. Use this to start with a clean slate.</p>
                    </div>
                </div>

                <div class="importer-right-column">
                    <div class="import-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div class="progress-text">Processing...</div>
                    </div>

                    <div class="import-log">
                        <h3>Import Log</h3>
                        <div class="log-controls">
                            <button id="clear-log-btn" class="button button-small">Clear Log</button>
                            <button id="export-log-btn" class="button button-small">Export Log</button>
                        </div>
                        <div id="log-content">
                            <div class="log-entry log-info">
                                <span class="timestamp">[Ready]</span> No import activity yet.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function simulate_import() {
        check_ajax_referer('petting_zoo_importer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $json_data = json_decode(stripslashes($_POST['json_data']), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error('Invalid JSON data: ' . json_last_error_msg());
        }

        $log = array();
        $errors = array();
        $warnings = array();

        // Handle different JSON structures
        $places_data = null;

        // Check if it's a single complete zoo object (new structure)
        if (isset($json_data['displayName']) || isset($json_data['name']) || isset($json_data['id'])) {
            $places_data = array($json_data); // Wrap single zoo in array
            $log[] = 'Detected single complete petting zoo JSON structure';
        }
        // Handle legacy array structures
        elseif (isset($json_data['places']) && is_array($json_data['places'])) {
            $places_data = $json_data['places'];
            $log[] = 'Detected legacy "places" array structure';
        } elseif (isset($json_data['petting_zoos']) && is_array($json_data['petting_zoos'])) {
            $places_data = $json_data['petting_zoos'];
            $log[] = 'Detected legacy "petting_zoos" array structure';
        } else {
            wp_send_json_error('JSON must contain either a single petting zoo object or an array of petting zoos');
        }

        $log[] = 'Starting simulation for ' . count($places_data) . ' petting zoo(s)...';

        // Check for root-level pics and youtube data (legacy support)
        if (isset($json_data['pics'])) {
            $log[] = 'Found root-level pics data with ' . count($json_data['pics']) . ' entries';
        }
        if (isset($json_data['youtube'])) {
            $log[] = 'Found root-level YouTube video URL';
        }

        foreach ($places_data as $index => $zoo_data) {
            $zoo_log = $this->validate_zoo_data($zoo_data, $index + 1);
            $log = array_merge($log, $zoo_log['messages']);

            if (!empty($zoo_log['errors'])) {
                $errors = array_merge($errors, $zoo_log['errors']);
            }

            if (!empty($zoo_log['warnings'])) {
                $warnings = array_merge($warnings, $zoo_log['warnings']);
            }
        }

        $log[] = 'Simulation completed.';
        $log[] = 'Total errors: ' . count($errors);
        $log[] = 'Total warnings: ' . count($warnings);

        wp_send_json_success(array(
            'log' => $log,
            'errors' => $errors,
            'warnings' => $warnings,
            'can_import' => empty($errors)
        ));
    }
    
    public function process_import() {
        check_ajax_referer('petting_zoo_importer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $json_data = json_decode(stripslashes($_POST['json_data']), true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error('Invalid JSON data: ' . json_last_error_msg());
        }

        $log = array();
        $imported = 0;
        $updated = 0;
        $errors = array();

        // Handle different JSON structures
        $places_data = null;

        // Check if it's a single complete zoo object (new structure)
        if (isset($json_data['displayName']) || isset($json_data['name']) || isset($json_data['id'])) {
            $places_data = array($json_data); // Wrap single zoo in array
            $log[] = 'Detected single complete petting zoo JSON structure';
        }
        // Handle legacy array structures
        elseif (isset($json_data['places']) && is_array($json_data['places'])) {
            $places_data = $json_data['places'];
            $log[] = 'Detected legacy "places" array structure';
        } elseif (isset($json_data['petting_zoos']) && is_array($json_data['petting_zoos'])) {
            $places_data = $json_data['petting_zoos'];
            $log[] = 'Detected legacy "petting_zoos" array structure';
        } else {
            wp_send_json_error('JSON must contain either a single petting zoo object or an array of petting zoos');
        }

        $log[] = 'Starting import for ' . count($places_data) . ' petting zoo(s)...';

        // Extract root-level pics and youtube data (legacy support)
        $root_pics = isset($json_data['pics']) ? $json_data['pics'] : null;
        $root_youtube = isset($json_data['youtube']) ? $json_data['youtube'] : null;

        foreach ($places_data as $index => $zoo_data) {
            $result = $this->import_zoo($zoo_data, $index + 1, $root_pics, $root_youtube);
            $log = array_merge($log, $result['messages']);

            if ($result['success']) {
                if ($result['action'] === 'created') {
                    $imported++;
                } else {
                    $updated++;
                }
            } else {
                $errors = array_merge($errors, $result['errors']);
            }
        }
        
        $log[] = 'Import completed.';
        $log[] = 'Created: ' . $imported . ' petting zoos';
        $log[] = 'Updated: ' . $updated . ' petting zoos';
        $log[] = 'Errors: ' . count($errors);
        
        wp_send_json_success(array(
            'log' => $log,
            'imported' => $imported,
            'updated' => $updated,
            'errors' => $errors
        ));
    }
    
    private function validate_zoo_data($zoo_data, $index) {
        $messages = array();
        $errors = array();
        $warnings = array();

        $messages[] = "Validating zoo #{$index}...";

        // Extract zoo name from different possible fields
        $zoo_name = '';
        if (isset($zoo_data['zooName'])) {
            $zoo_name = $zoo_data['zooName'];
        } elseif (isset($zoo_data['displayName']['text'])) {
            $zoo_name = $zoo_data['displayName']['text'];
        } elseif (isset($zoo_data['name'])) {
            $zoo_name = $zoo_data['name'];
        }

        // Required fields validation
        if (empty($zoo_name)) {
            $errors[] = "Zoo #{$index}: Missing zoo name (zooName, displayName.text, or name)";
        }

        // Check for description/intro
        if (empty($zoo_data['intro']) && empty($zoo_data['description'])) {
            $warnings[] = "Zoo #{$index}: No description or intro found";
        }

        // Validate name uniqueness
        if (!empty($zoo_name)) {
            $existing = get_page_by_title($zoo_name, OBJECT, 'petting_zoo');
            if ($existing) {
                $warnings[] = "Zoo #{$index}: '{$zoo_name}' already exists (will be updated)";
            }
        }

        // Validate coordinates
        if (isset($zoo_data['location']['latitude']) && isset($zoo_data['location']['longitude'])) {
            if (!is_numeric($zoo_data['location']['latitude']) || !is_numeric($zoo_data['location']['longitude'])) {
                $errors[] = "Zoo #{$index}: Invalid coordinates";
            }
        } elseif (isset($zoo_data['latitude']) && isset($zoo_data['longitude'])) {
            if (!is_numeric($zoo_data['latitude']) || !is_numeric($zoo_data['longitude'])) {
                $errors[] = "Zoo #{$index}: Invalid coordinates";
            }
        }

        // Validate address structure
        if (isset($zoo_data['postalAddress'])) {
            if (empty($zoo_data['postalAddress']['locality']) || empty($zoo_data['postalAddress']['administrativeArea'])) {
                $warnings[] = "Zoo #{$index}: Incomplete address information";
            }
        }

        if (empty($errors)) {
            $messages[] = "Zoo #{$index}: Validation passed";
        }

        return array(
            'messages' => $messages,
            'errors' => $errors,
            'warnings' => $warnings
        );
    }
    
    private function import_zoo($zoo_data, $index, $root_pics = null, $root_youtube = null) {
        $messages = array();
        $errors = array();

        try {
            // Extract zoo name from different possible fields
            $zoo_name = '';
            if (isset($zoo_data['zooName'])) {
                $zoo_name = $zoo_data['zooName'];
            } elseif (isset($zoo_data['displayName']['text'])) {
                $zoo_name = $zoo_data['displayName']['text'];
            } elseif (isset($zoo_data['name'])) {
                $zoo_name = $zoo_data['name'];
            }

            $messages[] = "Importing zoo #{$index}: {$zoo_name}...";

            // Check if zoo already exists
            $existing = get_page_by_title($zoo_name, OBJECT, 'petting_zoo');
            $action = 'created';

            if ($existing) {
                // Delete existing zoo data
                wp_delete_post($existing->ID, true);
                $messages[] = "Deleted existing zoo data for '{$zoo_name}'";
                $action = 'updated';
            }

            // Prepare content and excerpt
            $content = '';
            $excerpt = '';

            if (isset($zoo_data['intro'])) {
                $content = wp_kses_post($zoo_data['intro']);
                $excerpt = wp_trim_words($zoo_data['intro'], 30);
            } elseif (isset($zoo_data['description'])) {
                $content = wp_kses_post($zoo_data['description']);
                $excerpt = wp_trim_words($zoo_data['description'], 30);
            }

            // Add meta description if available
            if (isset($zoo_data['meta_description'])) {
                $excerpt = sanitize_text_field($zoo_data['meta_description']);
            }

            // Create new post
            $post_data = array(
                'post_title' => sanitize_text_field($zoo_name),
                'post_content' => $content,
                'post_status' => 'publish',
                'post_type' => 'petting_zoo',
                'post_excerpt' => $excerpt
            );

            $post_id = wp_insert_post($post_data);

            if (is_wp_error($post_id)) {
                $errors[] = "Failed to create post: " . $post_id->get_error_message();
                return array('success' => false, 'messages' => $messages, 'errors' => $errors);
            }

            // Save meta fields from the new JSON structure
            $this->save_zoo_meta_fields($post_id, $zoo_data, $messages, $root_pics, $root_youtube);

            // Handle taxonomies from the new JSON structure
            $this->save_zoo_taxonomies($post_id, $zoo_data, $messages);

            // Handle featured image if provided
            if (isset($zoo_data['featured_image']) && !empty($zoo_data['featured_image'])) {
                $this->handle_featured_image($post_id, $zoo_data['featured_image']);
            }

            $messages[] = "Successfully {$action} zoo: {$zoo_name} (ID: {$post_id})";

            return array(
                'success' => true,
                'action' => $action,
                'messages' => $messages,
                'errors' => $errors,
                'post_id' => $post_id
            );

        } catch (Exception $e) {
            $errors[] = "Exception during import: " . $e->getMessage();
            return array('success' => false, 'messages' => $messages, 'errors' => $errors);
        }
    }
    
    private function handle_featured_image($post_id, $image_url) {
        // This is a simplified version - you might want to enhance this
        // to handle local files or validate image URLs
        if (filter_var($image_url, FILTER_VALIDATE_URL)) {
            update_post_meta($post_id, '_featured_image_url', $image_url);
        }
    }

    private function save_zoo_meta_fields($post_id, $zoo_data, &$messages, $root_pics = null, $root_youtube = null) {
        // Address from postalAddress structure
        if (isset($zoo_data['postalAddress'])) {
            $address_parts = array();
            if (isset($zoo_data['postalAddress']['addressLines'])) {
                $address_parts[] = implode(', ', $zoo_data['postalAddress']['addressLines']);
            }
            if (isset($zoo_data['postalAddress']['locality'])) {
                $address_parts[] = $zoo_data['postalAddress']['locality'];
            }
            if (isset($zoo_data['postalAddress']['administrativeArea'])) {
                $address_parts[] = $zoo_data['postalAddress']['administrativeArea'];
            }
            if (isset($zoo_data['postalAddress']['postalCode'])) {
                $address_parts[] = $zoo_data['postalAddress']['postalCode'];
            }

            $full_address = implode(', ', $address_parts);
            update_post_meta($post_id, '_petting_zoo_address', sanitize_text_field($full_address));
        }

        // Phone number
        if (isset($zoo_data['nationalPhoneNumber'])) {
            update_post_meta($post_id, '_petting_zoo_phone', sanitize_text_field($zoo_data['nationalPhoneNumber']));
        }

        // Website
        if (isset($zoo_data['websiteUri'])) {
            update_post_meta($post_id, '_petting_zoo_website', esc_url_raw($zoo_data['websiteUri']));
        }

        // Hours from currentOpeningHours
        if (isset($zoo_data['currentOpeningHours']['weekdayDescriptions'])) {
            $hours = implode("\n", $zoo_data['currentOpeningHours']['weekdayDescriptions']);
            update_post_meta($post_id, '_petting_zoo_hours', sanitize_textarea_field($hours));
        }

        // Coordinates from location object
        if (isset($zoo_data['location']['latitude'])) {
            update_post_meta($post_id, '_petting_zoo_latitude', sanitize_text_field($zoo_data['location']['latitude']));
        }
        if (isset($zoo_data['location']['longitude'])) {
            update_post_meta($post_id, '_petting_zoo_longitude', sanitize_text_field($zoo_data['location']['longitude']));
        }

        // Additional custom fields from complete JSON structure
        $custom_fields = array(
            'meta_description' => '_petting_zoo_meta_description',
            'best_time_to_visit_description' => '_petting_zoo_best_time',
            'visitor_tips_for_families_description' => '_petting_zoo_visitor_tips',
            'food_and_amenities_description' => '_petting_zoo_food_amenities',
            'reviews_and_testimonials_description' => '_petting_zoo_reviews_description',
            'intro' => '_petting_zoo_intro',
            'businessStatus' => '_petting_zoo_business_status',
            'primaryType' => '_petting_zoo_primary_type'
        );

        foreach ($custom_fields as $json_field => $meta_key) {
            if (isset($zoo_data[$json_field])) {
                update_post_meta($post_id, $meta_key, sanitize_textarea_field($zoo_data[$json_field]));
            }
        }

        // Save complex data as JSON
        if (isset($zoo_data['animals_you_can_meet'])) {
            update_post_meta($post_id, '_petting_zoo_animals_data', wp_json_encode($zoo_data['animals_you_can_meet']));
        }

        if (isset($zoo_data['activities'])) {
            update_post_meta($post_id, '_petting_zoo_activities_data', wp_json_encode($zoo_data['activities']));
        }

        if (isset($zoo_data['pros_cons'])) {
            update_post_meta($post_id, '_petting_zoo_pros_cons', wp_json_encode($zoo_data['pros_cons']));
        }

        if (isset($zoo_data['faq'])) {
            update_post_meta($post_id, '_petting_zoo_faq', wp_json_encode($zoo_data['faq']));
        }

        // Save nearby places data
        if (isset($zoo_data['nearby'])) {
            update_post_meta($post_id, '_petting_zoo_nearby', wp_json_encode($zoo_data['nearby']));
        }

        // Google-specific data
        if (isset($zoo_data['rating'])) {
            update_post_meta($post_id, '_petting_zoo_rating', sanitize_text_field($zoo_data['rating']));
        }

        if (isset($zoo_data['userRatingCount'])) {
            update_post_meta($post_id, '_petting_zoo_rating_count', sanitize_text_field($zoo_data['userRatingCount']));
        }

        if (isset($zoo_data['googleMapsUri'])) {
            update_post_meta($post_id, '_petting_zoo_google_maps_url', esc_url_raw($zoo_data['googleMapsUri']));
        }

        // Save additional JSON fields
        if (isset($zoo_data['goodForChildren'])) {
            update_post_meta($post_id, '_petting_zoo_good_for_children', $zoo_data['goodForChildren'] ? 'yes' : 'no');
        }

        if (isset($zoo_data['paymentOptions'])) {
            update_post_meta($post_id, '_petting_zoo_payment_options', wp_json_encode($zoo_data['paymentOptions']));
        }

        if (isset($zoo_data['parkingOptions'])) {
            update_post_meta($post_id, '_petting_zoo_parking_options', wp_json_encode($zoo_data['parkingOptions']));
        }

        if (isset($zoo_data['accessibilityOptions'])) {
            update_post_meta($post_id, '_petting_zoo_accessibility_options', wp_json_encode($zoo_data['accessibilityOptions']));
        }

        if (isset($zoo_data['reviews'])) {
            update_post_meta($post_id, '_petting_zoo_reviews', wp_json_encode($zoo_data['reviews']));
        }

        // Handle photos from new JSON structure
        if (isset($zoo_data['photos']) && is_array($zoo_data['photos'])) {
            $processed_pics = array();
            foreach ($zoo_data['photos'] as $photo_data) {
                if (is_array($photo_data)) {
                    // Handle the structure like {"pic01": "url", "pic02": "url", etc.}
                    foreach ($photo_data as $pic_key => $pic_url) {
                        if (!empty($pic_url)) {
                            $processed_pics[] = array(
                                'key' => sanitize_text_field($pic_key),
                                'url' => esc_url_raw($pic_url),
                                'original_path' => sanitize_text_field($pic_url)
                            );
                        }
                    }
                }
            }

            if (!empty($processed_pics)) {
                update_post_meta($post_id, '_petting_zoo_pics', wp_json_encode($processed_pics));
                $messages[] = "Saved " . count($processed_pics) . " pictures for zoo";
            }
        }
        // Handle pics from root level (legacy support)
        elseif ($root_pics && is_array($root_pics)) {
            $processed_pics = array();
            foreach ($root_pics as $pic_data) {
                if (is_array($pic_data)) {
                    // Handle the structure like {"pic1": "path", "pic2": "path", etc.}
                    foreach ($pic_data as $pic_key => $pic_path) {
                        if (!empty($pic_path)) {
                            // Convert absolute path to relative URL if it's a local file
                            $pic_url = $this->convert_path_to_url($pic_path);
                            $processed_pics[] = array(
                                'key' => sanitize_text_field($pic_key),
                                'url' => esc_url_raw($pic_url),
                                'original_path' => sanitize_text_field($pic_path)
                            );
                        }
                    }
                } elseif (is_string($pic_data) && !empty($pic_data)) {
                    // Handle direct string URLs
                    $pic_url = $this->convert_path_to_url($pic_data);
                    $processed_pics[] = array(
                        'url' => esc_url_raw($pic_url),
                        'original_path' => sanitize_text_field($pic_data)
                    );
                }
            }

            if (!empty($processed_pics)) {
                update_post_meta($post_id, '_petting_zoo_pics', wp_json_encode($processed_pics));
                $messages[] = "Saved " . count($processed_pics) . " pictures for zoo";
            }
        }

        // Handle YouTube video from new JSON structure
        if (isset($zoo_data['zoo_yt_video_url']) && !empty($zoo_data['zoo_yt_video_url'])) {
            $youtube_url = esc_url_raw($zoo_data['zoo_yt_video_url']);
            update_post_meta($post_id, '_petting_zoo_youtube', $youtube_url);
            $messages[] = "Saved YouTube video URL for zoo";
        }
        // Handle YouTube video from root level (legacy support)
        elseif ($root_youtube && !empty($root_youtube)) {
            $youtube_url = esc_url_raw($root_youtube);
            update_post_meta($post_id, '_petting_zoo_youtube', $youtube_url);
            $messages[] = "Saved YouTube video URL for zoo";
        }

        $messages[] = "Saved meta fields for zoo";
    }

    private function convert_path_to_url($path) {
        // Convert Windows-style absolute paths to relative URLs
        if (strpos($path, 'C:\\') === 0 || strpos($path, 'c:\\') === 0) {
            // Extract the part after wp-content
            $wp_content_pos = strpos($path, 'wp-content');
            if ($wp_content_pos !== false) {
                $relative_path = substr($path, $wp_content_pos);
                // Convert backslashes to forward slashes
                $relative_path = str_replace('\\', '/', $relative_path);
                // Return as relative URL
                return '/' . $relative_path;
            }
        }

        // If it's already a URL, return as is
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            return $path;
        }

        // Otherwise, assume it's a relative path and return as is
        return $path;
    }

    private function save_zoo_taxonomies($post_id, $zoo_data, &$messages) {
        // Enhanced logging for taxonomy operations
        error_log("[Petting Zoo Importer] Processing taxonomies for post ID: {$post_id}");

        // Location taxonomy from postalAddress
        if (isset($zoo_data['postalAddress']['administrativeArea']) && isset($zoo_data['postalAddress']['locality'])) {
            $state = $zoo_data['postalAddress']['administrativeArea'];
            $city = $zoo_data['postalAddress']['locality'];

            error_log("[Petting Zoo Importer] Processing location: {$city}, {$state}");

            // Create or get state term (parent)
            $state_term = get_term_by('name', $state, 'location');
            if (!$state_term) {
                $state_result = wp_insert_term($state, 'location', array('parent' => 0));
                if (!is_wp_error($state_result)) {
                    $state_term_id = $state_result['term_id'];
                    $messages[] = "✅ Created new state term: {$state}";
                    error_log("[Petting Zoo Importer] Created state term: {$state} (ID: {$state_term_id})");
                } else {
                    $messages[] = "❌ Error creating state term: " . $state_result->get_error_message();
                    error_log("[Petting Zoo Importer] Error creating state term: " . $state_result->get_error_message());
                    return;
                }
            } else {
                $state_term_id = $state_term->term_id;
                error_log("[Petting Zoo Importer] Found existing state term: {$state} (ID: {$state_term_id})");
            }

            // Create or get city term under state (child)
            $existing_city_terms = get_terms(array(
                'taxonomy' => 'location',
                'name' => $city,
                'parent' => $state_term_id,
                'hide_empty' => false
            ));

            if (empty($existing_city_terms)) {
                $city_result = wp_insert_term($city, 'location', array('parent' => $state_term_id));
                if (!is_wp_error($city_result)) {
                    $city_term_id = $city_result['term_id'];
                    $messages[] = "✅ Created new city term: {$city} under {$state}";
                    error_log("[Petting Zoo Importer] Created city term: {$city} under {$state} (ID: {$city_term_id})");
                } else {
                    $messages[] = "❌ Error creating city term: " . $city_result->get_error_message();
                    error_log("[Petting Zoo Importer] Error creating city term: " . $city_result->get_error_message());
                    return;
                }
            } else {
                $city_term_id = $existing_city_terms[0]->term_id;
                error_log("[Petting Zoo Importer] Found existing city term: {$city} under {$state} (ID: {$city_term_id})");
            }

            // Assign both state and city to the post
            $term_assignment = wp_set_post_terms($post_id, array($state_term_id, $city_term_id), 'location');
            if (!is_wp_error($term_assignment)) {
                $messages[] = "✅ Assigned location terms: {$state} (ID: {$state_term_id}), {$city} (ID: {$city_term_id})";
                error_log("[Petting Zoo Importer] Successfully assigned location terms to post {$post_id}");
            } else {
                $messages[] = "❌ Error assigning location terms: " . $term_assignment->get_error_message();
                error_log("[Petting Zoo Importer] Error assigning location terms: " . $term_assignment->get_error_message());
            }
        } else {
            error_log("[Petting Zoo Importer] Missing location data in postalAddress");
        }

        // Animal types from animals_you_can_meet
        if (isset($zoo_data['animals_you_can_meet']['listofAnimals'])) {
            $animal_term_ids = array();
            foreach ($zoo_data['animals_you_can_meet']['listofAnimals'] as $animal) {
                $animal_name = ucfirst(trim($animal));
                $term = get_term_by('name', $animal_name, 'animal_type');

                if (!$term) {
                    $result = wp_insert_term($animal_name, 'animal_type');
                    if (!is_wp_error($result)) {
                        $animal_term_ids[] = $result['term_id'];
                        $messages[] = "Created new animal type: {$animal_name}";
                    }
                } else {
                    $animal_term_ids[] = $term->term_id;
                }
            }

            if (!empty($animal_term_ids)) {
                wp_set_post_terms($post_id, $animal_term_ids, 'animal_type');
            }
        }

        // Zoo type from types array or primaryType
        $zoo_types = array();
        if (isset($zoo_data['primaryType'])) {
            $zoo_types[] = $zoo_data['primaryType'];
        }
        if (isset($zoo_data['types']) && is_array($zoo_data['types'])) {
            $zoo_types = array_merge($zoo_types, $zoo_data['types']);
        }

        // Map Google types to our zoo types
        $type_mapping = array(
            'zoo' => 'Zoo',
            'amusement_park' => 'Amusement Park',
            'tourist_attraction' => 'Tourist Attraction',
            'establishment' => 'Establishment'
        );

        $zoo_type_term_ids = array();
        foreach ($zoo_types as $type) {
            $mapped_type = isset($type_mapping[$type]) ? $type_mapping[$type] : ucfirst(str_replace('_', ' ', $type));

            $term = get_term_by('name', $mapped_type, 'zoo_type');
            if (!$term) {
                $result = wp_insert_term($mapped_type, 'zoo_type');
                if (!is_wp_error($result)) {
                    $zoo_type_term_ids[] = $result['term_id'];
                    $messages[] = "Created new zoo type: {$mapped_type}";
                }
            } else {
                $zoo_type_term_ids[] = $term->term_id;
            }
        }

        if (!empty($zoo_type_term_ids)) {
            wp_set_post_terms($post_id, $zoo_type_term_ids, 'zoo_type');
        }

        // Features from various data points
        $features = array();

        // From accessibility options
        if (isset($zoo_data['accessibilityOptions'])) {
            if (!empty($zoo_data['accessibilityOptions']['wheelchairAccessibleEntrance'])) {
                $features[] = 'Wheelchair Accessible';
            }
            if (!empty($zoo_data['accessibilityOptions']['wheelchairAccessibleParking'])) {
                $features[] = 'Accessible Parking';
            }
        }

        // From parking options
        if (isset($zoo_data['parkingOptions'])) {
            if (!empty($zoo_data['parkingOptions']['paidParkingLot']) || !empty($zoo_data['parkingOptions']['paidGarageParking'])) {
                $features[] = 'Parking Available';
            }
        }

        // From activities data
        if (isset($zoo_data['activities'])) {
            if (!empty($zoo_data['activities']['playgroundAvailable']) && $zoo_data['activities']['playgroundAvailable'] === 'Yes') {
                $features[] = 'Playground';
            }
            if (!empty($zoo_data['activities']['mazeAvailable']) && $zoo_data['activities']['mazeAvailable'] === 'Yes') {
                $features[] = 'Maze';
            }
            if (!empty($zoo_data['activities']['rideAvailable']) && $zoo_data['activities']['rideAvailable'] === 'Yes') {
                $features[] = 'Rides';
            }
        }

        // Good for children
        if (isset($zoo_data['goodForChildren']) && $zoo_data['goodForChildren']) {
            $features[] = 'Family Friendly';
        }

        // Payment options
        if (isset($zoo_data['paymentOptions'])) {
            if (!empty($zoo_data['paymentOptions']['acceptsDebitCards'])) {
                $features[] = 'Card Payments';
            }
            if (!empty($zoo_data['paymentOptions']['acceptsNfc'])) {
                $features[] = 'Contactless Payment';
            }
        }

        $feature_term_ids = array();
        foreach ($features as $feature) {
            $term = get_term_by('name', $feature, 'features');
            if (!$term) {
                $result = wp_insert_term($feature, 'features');
                if (!is_wp_error($result)) {
                    $feature_term_ids[] = $result['term_id'];
                    $messages[] = "Created new feature: {$feature}";
                }
            } else {
                $feature_term_ids[] = $term->term_id;
            }
        }

        if (!empty($feature_term_ids)) {
            wp_set_post_terms($post_id, $feature_term_ids, 'features');
        }

        // Event types - default common ones for zoos
        $event_types = array('Family Visits', 'Educational Tours');
        if (isset($zoo_data['goodForChildren']) && $zoo_data['goodForChildren']) {
            $event_types[] = 'Birthday Parties';
            $event_types[] = 'School Field Trips';
        }

        $event_term_ids = array();
        foreach ($event_types as $event_type) {
            $term = get_term_by('name', $event_type, 'event_type');
            if (!$term) {
                $result = wp_insert_term($event_type, 'event_type');
                if (!is_wp_error($result)) {
                    $event_term_ids[] = $result['term_id'];
                    $messages[] = "Created new event type: {$event_type}";
                }
            } else {
                $event_term_ids[] = $term->term_id;
            }
        }

        if (!empty($event_term_ids)) {
            wp_set_post_terms($post_id, $event_term_ids, 'event_type');
        }

        $messages[] = "Saved taxonomy terms for zoo";
    }

    /**
     * Handle bulk import of multiple JSON files
     */
    public function bulk_import() {
        check_ajax_referer('petting_zoo_importer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $files_data = json_decode(stripslashes($_POST['files_data']), true);
        $simulate = isset($_POST['simulate']) && $_POST['simulate'] === 'true';

        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error('Invalid files data: ' . json_last_error_msg());
        }

        $log = array();
        $total_imported = 0;
        $total_updated = 0;
        $total_errors = 0;
        $file_count = count($files_data);

        $log[] = ($simulate ? 'Starting bulk simulation' : 'Starting bulk import') . " for {$file_count} file(s)...";

        foreach ($files_data as $index => $file_info) {
            $file_number = $index + 1;
            $filename = $file_info['filename'];
            $json_data = $file_info['data'];

            $log[] = "Processing file {$file_number}/{$file_count}: {$filename}";

            if ($simulate) {
                $result = $this->simulate_single_file($json_data, $filename);
            } else {
                $result = $this->process_single_file($json_data, $filename);
            }

            $log = array_merge($log, $result['messages']);

            if (!$simulate) {
                $total_imported += $result['imported'];
                $total_updated += $result['updated'];
            }

            if (!empty($result['errors'])) {
                $total_errors += count($result['errors']);
                $log = array_merge($log, $result['errors']);
            }

            // Add separator between files
            if ($file_number < $file_count) {
                $log[] = "--- End of {$filename} ---";
            }
        }

        $log[] = ($simulate ? 'Bulk simulation completed' : 'Bulk import completed') . "!";

        if (!$simulate) {
            $log[] = "Summary: {$total_imported} imported, {$total_updated} updated, {$total_errors} errors";
        }

        wp_send_json_success(array(
            'log' => $log,
            'imported' => $total_imported,
            'updated' => $total_updated,
            'errors' => $total_errors
        ));
    }

    /**
     * Simulate import for a single file
     */
    private function simulate_single_file($json_data, $filename) {
        $log = array();
        $errors = array();
        $warnings = array();

        // Handle different JSON structures
        $places_data = null;

        // Check if it's a single complete zoo object (new structure)
        if (isset($json_data['displayName']) || isset($json_data['name']) || isset($json_data['id'])) {
            $places_data = array($json_data); // Wrap single zoo in array
            $log[] = "Detected single complete petting zoo JSON structure in {$filename}";
        } elseif (isset($json_data['petting_zoos'])) {
            $places_data = $json_data['petting_zoos'];
            $log[] = "Detected legacy 'petting_zoos' array structure in {$filename}";
        } elseif (isset($json_data['places'])) {
            $places_data = $json_data['places'];
            $log[] = "Detected legacy 'places' array structure in {$filename}";
        } else {
            $errors[] = "Unknown JSON structure in {$filename}";
            return array('messages' => $log, 'errors' => $errors, 'warnings' => $warnings);
        }

        $log[] = "Found " . count($places_data) . " petting zoo(s) in {$filename}";

        foreach ($places_data as $index => $zoo_data) {
            $zoo_log = $this->validate_zoo_data($zoo_data, $index + 1);
            $log = array_merge($log, $zoo_log['messages']);

            if (!empty($zoo_log['errors'])) {
                $errors = array_merge($errors, $zoo_log['errors']);
            }

            if (!empty($zoo_log['warnings'])) {
                $warnings = array_merge($warnings, $zoo_log['warnings']);
            }
        }

        return array(
            'messages' => $log,
            'errors' => $errors,
            'warnings' => $warnings
        );
    }

    /**
     * Process import for a single file
     */
    private function process_single_file($json_data, $filename) {
        $log = array();
        $imported = 0;
        $updated = 0;
        $errors = array();

        // Handle different JSON structures
        $places_data = null;

        // Check if it's a single complete zoo object (new structure)
        if (isset($json_data['displayName']) || isset($json_data['name']) || isset($json_data['id'])) {
            $places_data = array($json_data); // Wrap single zoo in array
            $log[] = "Detected single complete petting zoo JSON structure in {$filename}";
        } elseif (isset($json_data['petting_zoos'])) {
            $places_data = $json_data['petting_zoos'];
            $log[] = "Detected legacy 'petting_zoos' array structure in {$filename}";
        } elseif (isset($json_data['places'])) {
            $places_data = $json_data['places'];
            $log[] = "Detected legacy 'places' array structure in {$filename}";
        } else {
            $errors[] = "Unknown JSON structure in {$filename}";
            return array('messages' => $log, 'errors' => $errors, 'imported' => 0, 'updated' => 0);
        }

        $log[] = "Processing " . count($places_data) . " petting zoo(s) from {$filename}";

        // Extract root-level pics and youtube data (legacy support)
        $root_pics = isset($json_data['pics']) ? $json_data['pics'] : null;
        $root_youtube = isset($json_data['youtube']) ? $json_data['youtube'] : null;

        foreach ($places_data as $index => $zoo_data) {
            $result = $this->import_zoo($zoo_data, $index + 1, $root_pics, $root_youtube);
            $log = array_merge($log, $result['messages']);

            if ($result['success']) {
                if ($result['action'] === 'created') {
                    $imported++;
                } else {
                    $updated++;
                }
            } else {
                $errors = array_merge($errors, $result['errors']);
            }
        }

        return array(
            'messages' => $log,
            'errors' => $errors,
            'imported' => $imported,
            'updated' => $updated
        );
    }

    /**
     * Clear all petting zoo related data
     */
    public function clear_all_data() {
        check_ajax_referer('petting_zoo_importer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $log = array();
        $errors = array();

        try {
            $log[] = "Starting data cleanup process...";

            // Delete all petting zoo posts
            $petting_zoos = get_posts(array(
                'post_type' => 'petting_zoo',
                'posts_per_page' => -1,
                'post_status' => 'any'
            ));

            $deleted_posts = 0;
            foreach ($petting_zoos as $zoo) {
                if (wp_delete_post($zoo->ID, true)) {
                    $deleted_posts++;
                } else {
                    $errors[] = "Failed to delete petting zoo: {$zoo->post_title}";
                }
            }
            $log[] = "Deleted {$deleted_posts} petting zoo posts";

            // Clear taxonomies
            $taxonomies = array('location', 'animal_type', 'zoo_type', 'features', 'event_type');
            $deleted_terms = 0;

            foreach ($taxonomies as $taxonomy) {
                $terms = get_terms(array(
                    'taxonomy' => $taxonomy,
                    'hide_empty' => false
                ));

                if (!is_wp_error($terms)) {
                    foreach ($terms as $term) {
                        if (wp_delete_term($term->term_id, $taxonomy)) {
                            $deleted_terms++;
                        } else {
                            $errors[] = "Failed to delete term: {$term->name} from {$taxonomy}";
                        }
                    }
                }
            }
            $log[] = "Deleted {$deleted_terms} taxonomy terms";

            // Clean up orphaned meta data
            global $wpdb;
            $orphaned_meta = $wpdb->query("
                DELETE pm FROM {$wpdb->postmeta} pm
                LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID
                WHERE p.ID IS NULL AND pm.meta_key LIKE '_petting_zoo_%'
            ");

            if ($orphaned_meta !== false) {
                $log[] = "Cleaned up {$orphaned_meta} orphaned meta entries";
            }

            $log[] = "Data cleanup completed successfully!";
            $log[] = "All petting zoo data has been removed from the database.";

            wp_send_json_success(array(
                'log' => $log,
                'errors' => $errors,
                'deleted_posts' => $deleted_posts,
                'deleted_terms' => $deleted_terms
            ));

        } catch (Exception $e) {
            $errors[] = "Error during cleanup: " . $e->getMessage();
            wp_send_json_error(array(
                'log' => $log,
                'errors' => $errors
            ));
        }
    }
}

// Initialize the plugin
new PettingZooImporter();
